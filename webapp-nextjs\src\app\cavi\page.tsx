'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { useAuth } from '@/contexts/AuthContext'
import { caviApi } from '@/lib/api'
import { Cavo } from '@/types'
import {
  Cable,
  Package,
  CheckCircle,
  AlertCircle,
  Clock,
  TrendingUp,
  Activity,
  Loader2,
  BarChart3,
  Zap
} from 'lucide-react'

interface DashboardStats {
  totali: number
  installati: number
  collegati: number
  certificati: number
  percentualeInstallazione: number
  percentualeCollegamento: number
  percentualeCertificazione: number
  metriTotali: number
  metriInstallati: number
  metriCollegati: number
  metriCertificati: number
}

export default function CaviPage() {
  const { user, cantiere, isAuthenticated, isLoading } = useAuth()
  const router = useRouter()
  const [cavi, setCavi] = useState<Cavo[]>([])
  const [caviSpare, setCaviSpare] = useState<Cavo[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [stats, setStats] = useState<DashboardStats>({
    totali: 0,
    installati: 0,
    collegati: 0,
    certificati: 0,
    percentualeInstallazione: 0,
    percentualeCollegamento: 0,
    percentualeCertificazione: 0,
    metriTotali: 0,
    metriInstallati: 0,
    metriCollegati: 0,
    metriCertificati: 0
  })

  // Get cantiere ID
  const [cantiereId, setCantiereId] = useState<number>(0)

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/login')
    }
  }, [isAuthenticated, isLoading, router])

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const storedId = cantiere?.id_cantiere || parseInt(localStorage.getItem('selectedCantiereId') || '0')
      setCantiereId(storedId)
    }
  }, [cantiere])

  // Carica i cavi dal backend
  useEffect(() => {
    if (cantiereId && cantiereId > 0) {
      loadCavi()
    }
  }, [cantiereId])

  const loadCavi = async () => {
    try {
      setLoading(true)
      setError('')

      console.log('🔍 Tentativo caricamento cavi per cantiere:', cantiereId)
      console.log('🔍 Token presente:', !!localStorage.getItem('token'))

      // Prima prova con l'API normale
      try {
        const data = await caviApi.getCavi(cantiereId)
        console.log('✅ API normale riuscita, cavi ricevuti:', data?.length || 0)

        // Separa cavi attivi e spare
        const caviAttivi = data.filter((cavo: Cavo) => !cavo.spare)
        const caviSpareFiltered = data.filter((cavo: Cavo) => cavo.spare)

        setCavi(caviAttivi)
        setCaviSpare(caviSpareFiltered)

        // Calcola statistiche
        calculateStats(caviAttivi)

      } catch (apiError: any) {
        console.log('❌ API normale fallita, provo endpoint debug...')
        console.error('Errore API normale:', apiError)

        // Fallback: prova con endpoint debug (senza autenticazione)
        try {
          const response = await fetch(`http://localhost:8001/api/cavi/debug/${cantiereId}`)
          const debugData = await response.json()
          console.log('✅ Endpoint debug riuscito:', debugData)

          if (debugData.cavi && Array.isArray(debugData.cavi)) {
            const caviAttivi = debugData.cavi.filter((cavo: any) => !cavo.spare)
            const caviSpareFiltered = debugData.cavi.filter((cavo: any) => cavo.spare)

            setCavi(caviAttivi)
            setCaviSpare(caviSpareFiltered)
            calculateStats(caviAttivi)

            setError('⚠️ Dati caricati tramite endpoint debug (problema autenticazione)')
          } else {
            throw new Error('Formato dati debug non valido')
          }
        } catch (debugError) {
          console.error('❌ Anche endpoint debug fallito:', debugError)
          throw apiError // Rilancia l'errore originale
        }
      }

    } catch (error: any) {
      console.error('❌ Errore generale nel caricamento cavi:', error)
      setError(`Errore nel caricamento dei cavi: ${error.response?.data?.detail || error.message}`)
    } finally {
      setLoading(false)
    }
  }

  const calculateStats = (caviData: Cavo[]) => {
    const totali = caviData.length
    const installati = caviData.filter(c => c.metri_posati > 0).length
    const collegati = caviData.filter(c => c.collegamento === 3).length // 3 = collegato
    const certificati = caviData.filter(c => c.certificato).length

    const metriTotali = caviData.reduce((sum, c) => sum + (c.metri_teorici || 0), 0)
    const metriInstallati = caviData.reduce((sum, c) => sum + (c.metri_posati || 0), 0)
    const metriCollegati = caviData.filter(c => c.collegamento === 3).reduce((sum, c) => sum + (c.metri_posati || 0), 0)
    const metriCertificati = caviData.filter(c => c.certificato).reduce((sum, c) => sum + (c.metri_posati || 0), 0)

    setStats({
      totali,
      installati,
      collegati,
      certificati,
      percentualeInstallazione: totali > 0 ? Math.round((installati / totali) * 100) : 0,
      percentualeCollegamento: totali > 0 ? Math.round((collegati / totali) * 100) : 0,
      percentualeCertificazione: totali > 0 ? Math.round((certificati / totali) * 100) : 0,
      metriTotali,
      metriInstallati,
      metriCollegati,
      metriCertificati
    })
  }

  if (isLoading || loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  if (!cantiereId) {
    return (
      <div className="container mx-auto p-6">
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Nessun cantiere selezionato. Seleziona un cantiere per visualizzare i cavi.
          </AlertDescription>
        </Alert>
        <div className="mt-4 p-4 bg-gray-100 rounded">
          <h3 className="font-bold">Debug Info:</h3>
          <p>User: {user ? user.username : 'Non autenticato'}</p>
          <p>Cantiere context: {cantiere ? cantiere.commessa : 'Nessuno'}</p>
          <p>Token presente: {typeof window !== 'undefined' ? (localStorage.getItem('token') ? 'Sì' : 'No') : 'N/A'}</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="container mx-auto p-6">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
        <Button onClick={loadCavi} className="mt-4">
          Riprova
        </Button>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6">

      {/* Dashboard con statistiche avanzate */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        {/* Totale Cavi */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Totale Cavi</CardTitle>
            <Cable className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totali}</div>
            <p className="text-xs text-muted-foreground">
              {stats.metriTotali.toLocaleString()} metri totali
            </p>
          </CardContent>
        </Card>

        {/* Installazione */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Installazione</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.installati}</div>
            <div className="flex items-center space-x-2">
              <Badge variant={stats.percentualeInstallazione >= 80 ? "default" : "secondary"}>
                {stats.percentualeInstallazione}%
              </Badge>
              <p className="text-xs text-muted-foreground">
                {stats.metriInstallati.toLocaleString()}m
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Collegamento */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Collegamento</CardTitle>
            <Zap className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.collegati}</div>
            <div className="flex items-center space-x-2">
              <Badge variant={stats.percentualeCollegamento >= 80 ? "default" : "secondary"}>
                {stats.percentualeCollegamento}%
              </Badge>
              <p className="text-xs text-muted-foreground">
                {stats.metriCollegati.toLocaleString()}m
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Certificazione */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Certificazione</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.certificati}</div>
            <div className="flex items-center space-x-2">
              <Badge variant={stats.percentualeCertificazione >= 80 ? "default" : "secondary"}>
                {stats.percentualeCertificazione}%
              </Badge>
              <p className="text-xs text-muted-foreground">
                {stats.metriCertificati.toLocaleString()}m
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Placeholder per la tabella cavi - da implementare */}
      <Card>
        <CardHeader>
          <CardTitle>Elenco Cavi ({cavi.length})</CardTitle>
          <CardDescription>
            Gestione completa dei cavi con stato installazione, collegamento e certificazione
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <p className="text-muted-foreground">
              Tabella cavi in fase di implementazione...
            </p>
            <p className="text-sm text-muted-foreground mt-2">
              Cavi attivi: {cavi.length} | Cavi spare: {caviSpare.length}
            </p>

            {/* Debug info */}
            <div className="mt-6 p-4 bg-gray-100 rounded text-left">
              <h3 className="font-bold mb-2">Debug Info:</h3>
              <p><strong>User:</strong> {user ? user.username : 'Non autenticato'}</p>
              <p><strong>User Role:</strong> {user ? user.ruolo : 'N/A'}</p>
              <p><strong>Cantiere ID:</strong> {cantiereId}</p>
              <p><strong>Cantiere context:</strong> {cantiere ? cantiere.commessa : 'Nessuno'}</p>
              <p><strong>Token presente:</strong> {typeof window !== 'undefined' ? (localStorage.getItem('token') ? 'Sì' : 'No') : 'N/A'}</p>
              <p><strong>Loading:</strong> {loading ? 'Sì' : 'No'}</p>
              <p><strong>Error:</strong> {error || 'Nessuno'}</p>
              <p><strong>Cavi ricevuti:</strong> {cavi.length}</p>
              <p><strong>Cavi spare:</strong> {caviSpare.length}</p>

              <div className="mt-4">
                <Button
                  onClick={async () => {
                    try {
                      const response = await fetch('http://localhost:8001/api/cavi/debug/1')
                      const data = await response.json()
                      console.log('🔍 Test diretto backend:', data)
                      alert(`Backend ha ${data.total_cavi} cavi per cantiere 1`)
                    } catch (err) {
                      console.error('❌ Errore test backend:', err)
                      alert('Errore nel test backend')
                    }
                  }}
                  variant="outline"
                  size="sm"
                >
                  Test Backend Diretto
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}


