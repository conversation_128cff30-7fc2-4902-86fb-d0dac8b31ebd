"""
API Endpoints per la Gestione Sicura delle Password
Implementa i due flussi: cambio password e recupero password.
"""

from typing import Any, Optional
from datetime import datetime, timedelta
import hashlib
import secrets

from fastapi import APIRouter, Depends, HTTPException, status, Request
from sqlalchemy.orm import Session
from pydantic import BaseModel, EmailStr

from backend.database import get_db
from backend.models.user import User
from backend.models.cantiere import Cantiere
from backend.models.security_models import PasswordResetToken, SecurityEvent, RateLimiting
from backend.core.password_security import (
    password_validator, token_manager, rate_limiter, password_hasher
)
from backend.core.email_service import email_service
from backend.core.audit_logger import (
    log_password_change, log_password_reset_request, 
    log_password_reset_completion, log_rate_limit_exceeded
)
from backend.core.security import get_current_active_user

router = APIRouter()

# =====================================================
# SCHEMAS PER LE API
# =====================================================

class PasswordChangeRequest(BaseModel):
    """Schema per richiesta cambio password."""
    current_password: str
    new_password: str
    confirm_password: str

class PasswordResetRequest(BaseModel):
    """Schema per richiesta reset password."""
    email: EmailStr
    user_type: str = "user"  # "user" o "cantiere"

class PasswordResetConfirm(BaseModel):
    """Schema per conferma reset password."""
    token: str
    new_password: str
    confirm_password: str

class PasswordValidationResponse(BaseModel):
    """Schema per risposta validazione password."""
    is_valid: bool
    error_message: Optional[str] = None
    strength_score: int
    suggestions: list[str] = []

class PasswordChangeResponse(BaseModel):
    """Schema per risposta cambio password."""
    success: bool
    message: str

class PasswordResetResponse(BaseModel):
    """Schema per risposta reset password."""
    success: bool
    message: str

# =====================================================
# UTILITY FUNCTIONS
# =====================================================

def get_client_ip(request: Request) -> str:
    """Estrae l'IP del client dalla richiesta."""
    forwarded = request.headers.get("X-Forwarded-For")
    if forwarded:
        return forwarded.split(",")[0].strip()
    return request.client.host

def check_rate_limit(identifier: str, request: Request) -> None:
    """Controlla e applica rate limiting."""
    is_limited, seconds_until_reset = rate_limiter.is_rate_limited(identifier)
    
    if is_limited:
        log_rate_limit_exceeded(identifier, get_client_ip(request))
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail=f"Troppi tentativi. Riprova tra {seconds_until_reset} secondi."
        )
    
    rate_limiter.record_attempt(identifier)

def hash_token(token: str) -> str:
    """Genera hash sicuro del token per storage nel database."""
    return hashlib.sha256(token.encode()).hexdigest()

# =====================================================
# ENDPOINT: VALIDAZIONE PASSWORD
# =====================================================

@router.post("/validate-password", response_model=PasswordValidationResponse)
def validate_password(password: str) -> PasswordValidationResponse:
    """
    Valida la forza di una password secondo le politiche di sicurezza.
    """
    is_valid, error_message, strength_score = password_validator.validate_password(password)
    
    suggestions = []
    if not is_valid:
        if len(password) < 8:
            suggestions.append("Usa almeno 8 caratteri")
        if not any(c.isupper() for c in password):
            suggestions.append("Aggiungi almeno una lettera maiuscola")
        if not any(c.islower() for c in password):
            suggestions.append("Aggiungi almeno una lettera minuscola")
        if not any(c.isdigit() for c in password):
            suggestions.append("Aggiungi almeno un numero")
        if not any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password):
            suggestions.append("Aggiungi almeno un carattere speciale")
    
    return PasswordValidationResponse(
        is_valid=is_valid,
        error_message=error_message,
        strength_score=strength_score,
        suggestions=suggestions
    )

# =====================================================
# ENDPOINT: CAMBIO PASSWORD UTENTE
# =====================================================

@router.post("/change-password", response_model=PasswordChangeResponse)
def change_user_password(
    request: Request,
    password_data: PasswordChangeRequest,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> PasswordChangeResponse:
    """
    Cambia la password di un utente autenticato.
    Richiede la password attuale per sicurezza.
    """
    try:
        # Rate limiting per utente
        check_rate_limit(f"user_{current_user.id_utente}", request)
        
        # Validazione input
        if password_data.new_password != password_data.confirm_password:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Le nuove password non corrispondono"
            )
        
        # Validazione password attuale
        if not password_hasher.verify_password(password_data.current_password, current_user.password):
            log_password_change(
                current_user.id_utente, 
                current_user.username, 
                False, 
                get_client_ip(request),
                "Password attuale non corretta"
            )
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Password attuale non corretta"
            )
        
        # Validazione nuova password
        is_valid, error_message, _ = password_validator.validate_password(password_data.new_password)
        if not is_valid:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=error_message
            )
        
        # Controlla che la nuova password sia diversa da quella attuale
        if password_hasher.verify_password(password_data.new_password, current_user.password):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="La nuova password deve essere diversa da quella attuale"
            )
        
        # Aggiorna password
        new_password_hash = password_hasher.hash_password(password_data.new_password)
        current_user.password = new_password_hash
        current_user.last_password_change = datetime.utcnow()
        current_user.failed_login_attempts = 0  # Reset tentativi falliti
        current_user.locked_until = None  # Sblocca account se era bloccato
        
        # Rimuovi password in chiaro se presente (per sicurezza)
        if hasattr(current_user, 'password_plain'):
            current_user.password_plain = None
        
        db.commit()
        
        # Log successo
        log_password_change(
            current_user.id_utente,
            current_user.username,
            True,
            get_client_ip(request)
        )
        
        # Invia email di notifica
        if current_user.email:
            email_service.send_password_changed_notification(
                current_user.email,
                current_user.username,
                "utente"
            )
        
        return PasswordChangeResponse(
            success=True,
            message="Password cambiata con successo"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        log_password_change(
            current_user.id_utente,
            current_user.username,
            False,
            get_client_ip(request),
            str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Errore interno durante il cambio password"
        )

# =====================================================
# ENDPOINT: RICHIESTA RESET PASSWORD
# =====================================================

@router.post("/request-password-reset", response_model=PasswordResetResponse)
def request_password_reset(
    request: Request,
    reset_request: PasswordResetRequest,
    db: Session = Depends(get_db)
) -> PasswordResetResponse:
    """
    Richiede il reset della password via email.
    Supporta sia utenti che cantieri.
    """
    try:
        # Rate limiting per IP
        check_rate_limit(f"ip_{get_client_ip(request)}", request)
        
        user = None
        cantiere = None
        
        # Cerca utente o cantiere
        if reset_request.user_type == "user":
            user = db.query(User).filter(User.email == reset_request.email).first()
            if user and user.ruolo == "owner":
                # Non permettere reset per amministratore
                log_password_reset_request(
                    reset_request.email,
                    get_client_ip(request),
                    False,
                    "Reset non permesso per amministratore"
                )
                # Risposta generica per sicurezza
                return PasswordResetResponse(
                    success=True,
                    message="Se l'email è registrata, riceverai le istruzioni per il reset."
                )
        elif reset_request.user_type == "cantiere":
            # Per cantieri, cerca per email dell'utente proprietario
            cantiere = db.query(Cantiere).join(User).filter(User.email == reset_request.email).first()
        
        if not user and not cantiere:
            # Log tentativo su email non esistente
            log_password_reset_request(
                reset_request.email,
                get_client_ip(request),
                False,
                "Email non trovata"
            )
            # Risposta generica per sicurezza
            return PasswordResetResponse(
                success=True,
                message="Se l'email è registrata, riceverai le istruzioni per il reset."
            )
        
        # Genera token sicuro
        if user:
            token = token_manager.generate_reset_token(user.id_utente, user.email)
            target_id = user.id_utente
            target_name = user.username
        else:
            token = token_manager.generate_reset_token(cantiere.id_cantiere, reset_request.email)
            target_id = cantiere.id_cantiere
            target_name = cantiere.commessa
        
        # Salva token nel database
        token_hash = hash_token(token)
        reset_token = PasswordResetToken(
            token_hash=token_hash,
            user_id=user.id_utente if user else None,
            cantiere_id=cantiere.id_cantiere if cantiere else None,
            email=reset_request.email,
            expires_at=datetime.utcnow() + timedelta(minutes=30),
            ip_address=get_client_ip(request),
            user_agent=request.headers.get("User-Agent", "")
        )
        
        db.add(reset_token)
        db.commit()
        
        # Genera link di reset
        reset_link = f"{request.base_url}reset-password?token={token}"
        
        # Invia email
        email_sent = email_service.send_password_reset_email(
            reset_request.email,
            reset_link,
            target_name,
            reset_request.user_type
        )
        
        if email_sent:
            log_password_reset_request(
                reset_request.email,
                get_client_ip(request),
                True
            )
        else:
            log_password_reset_request(
                reset_request.email,
                get_client_ip(request),
                False,
                "Errore invio email"
            )
        
        # Risposta sempre positiva per sicurezza
        return PasswordResetResponse(
            success=True,
            message="Se l'email è registrata, riceverai le istruzioni per il reset."
        )
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        log_password_reset_request(
            reset_request.email,
            get_client_ip(request),
            False,
            str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Errore interno durante la richiesta di reset"
        )

# =====================================================
# ENDPOINT: CONFERMA RESET PASSWORD
# =====================================================

@router.post("/confirm-password-reset", response_model=PasswordChangeResponse)
def confirm_password_reset(
    request: Request,
    reset_data: PasswordResetConfirm,
    db: Session = Depends(get_db)
) -> PasswordChangeResponse:
    """
    Conferma il reset della password utilizzando il token ricevuto via email.
    """
    try:
        # Rate limiting per IP
        check_rate_limit(f"reset_{get_client_ip(request)}", request)

        # Validazione input
        if reset_data.new_password != reset_data.confirm_password:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Le password non corrispondono"
            )

        # Validazione token
        is_valid, token_data = token_manager.validate_reset_token(reset_data.token)
        if not is_valid:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Token non valido o scaduto"
            )

        # Cerca token nel database
        token_hash = hash_token(reset_data.token)
        db_token = db.query(PasswordResetToken).filter(
            PasswordResetToken.token_hash == token_hash,
            PasswordResetToken.used_at.is_(None)
        ).first()

        if not db_token or db_token.is_expired or db_token.is_used:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Token non valido o già utilizzato"
            )

        # Validazione nuova password
        is_valid, error_message, _ = password_validator.validate_password(reset_data.new_password)
        if not is_valid:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=error_message
            )

        # Aggiorna password
        new_password_hash = password_hasher.hash_password(reset_data.new_password)

        if db_token.user_id:
            # Reset password utente
            user = db.query(User).filter(User.id_utente == db_token.user_id).first()
            if not user:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Utente non trovato"
                )

            user.password = new_password_hash
            user.last_password_change = datetime.utcnow()
            user.failed_login_attempts = 0
            user.locked_until = None

            # Rimuovi password in chiaro se presente
            if hasattr(user, 'password_plain'):
                user.password_plain = None

            target_id = user.id_utente
            target_email = user.email
            target_name = user.username

        elif db_token.cantiere_id:
            # Reset password cantiere
            cantiere = db.query(Cantiere).filter(Cantiere.id_cantiere == db_token.cantiere_id).first()
            if not cantiere:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Cantiere non trovato"
                )

            cantiere.password_cantiere = new_password_hash
            cantiere.last_password_change = datetime.utcnow()
            cantiere.failed_login_attempts = 0
            cantiere.locked_until = None

            # Rimuovi password criptata se presente
            if hasattr(cantiere, 'password_cantiere_encrypted'):
                cantiere.password_cantiere_encrypted = None

            target_id = cantiere.id_cantiere
            target_email = db_token.email
            target_name = cantiere.commessa

        # Marca token come utilizzato
        db_token.used_at = datetime.utcnow()

        db.commit()

        # Log successo
        log_password_reset_completion(
            target_id,
            target_email,
            True,
            get_client_ip(request)
        )

        # Invia email di notifica
        user_type = "utente" if db_token.user_id else "cantiere"
        email_service.send_password_changed_notification(
            target_email,
            target_name,
            user_type
        )

        return PasswordChangeResponse(
            success=True,
            message="Password resettata con successo"
        )

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        log_password_reset_completion(
            0,  # ID sconosciuto in caso di errore
            reset_data.token[:10] + "...",  # Parte del token per debug
            False,
            get_client_ip(request),
            str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Errore interno durante il reset password"
        )

# =====================================================
# ENDPOINT: CLEANUP TOKENS SCADUTI
# =====================================================

@router.post("/cleanup-expired-tokens")
def cleanup_expired_tokens(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> dict:
    """
    Pulisce i token scaduti dal database.
    Solo per amministratori.
    """
    if current_user.ruolo != "owner":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Operazione riservata agli amministratori"
        )

    try:
        # Elimina token scaduti o utilizzati
        deleted_count = db.query(PasswordResetToken).filter(
            (PasswordResetToken.expires_at < datetime.utcnow()) |
            (PasswordResetToken.used_at.isnot(None))
        ).delete()

        db.commit()

        return {
            "success": True,
            "message": f"Eliminati {deleted_count} token scaduti"
        }

    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Errore durante la pulizia: {str(e)}"
        )
