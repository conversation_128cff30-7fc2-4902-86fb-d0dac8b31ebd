{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|public).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|public).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "DmFlnj8I3P1ljkWGhQVgM+UNdCE3hT3/mJhLA6xJySg=", "__NEXT_PREVIEW_MODE_ID": "11051e3a25c0ce5ccfc6200f0e1ab16f", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "688563<PERSON>de8b4e586c1d8fe1a8dfbab7cadccaec27a3dd3fc988b6a3e38d0342", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "bf1abf9a83946ea6fe04fc5112bff65235eb9d9d155e3e097f590a5764b9c4db"}}}, "sortedMiddleware": ["/"], "functions": {}}