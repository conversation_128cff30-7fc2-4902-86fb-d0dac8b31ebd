'use client'

import { useState, useRef, useEffect } from 'react'
import { createPortal } from 'react-dom'
import { ChevronDown, Edit, Clock, CheckCircle, Trash2 } from 'lucide-react'

interface CompactActionsDropdownProps {
  user: {
    id_utente: number
    ruolo: string
    abilitato: boolean
  }
  onEdit: () => void
  onToggleStatus: () => void
  onDelete: () => void
}

export default function CompactActionsDropdown({
  user,
  onEdit,
  onToggleStatus,
  onDelete
}: CompactActionsDropdownProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, left: 0, position: 'bottom' as 'bottom' | 'top' })
  const dropdownRef = useRef<HTMLDivElement>(null)
  const buttonRef = useRef<HTMLButtonElement>(null)

  // Calcola la posizione assoluta del dropdown
  const calculatePosition = () => {
    if (!buttonRef.current) return

    const buttonRect = buttonRef.current.getBoundingClientRect()
    const viewportHeight = window.innerHeight
    const dropdownHeight = 120 // Altezza stimata del dropdown
    const dropdownWidth = 144 // w-36 = 144px

    let top = buttonRect.bottom + 4 // mt-1
    let position: 'bottom' | 'top' = 'bottom'

    // Se non c'è spazio sotto, apri verso l'alto
    if (buttonRect.bottom + dropdownHeight > viewportHeight) {
      top = buttonRect.top - dropdownHeight - 4 // mb-1
      position = 'top'
    }

    // Calcola left per allineare a destra del pulsante
    const left = buttonRect.right - dropdownWidth

    setDropdownPosition({ top, left, position })
  }

  // Chiudi dropdown quando si clicca fuori
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const handleToggle = () => {
    if (!isOpen) {
      calculatePosition()
    }
    setIsOpen(!isOpen)
  }

  const handleAction = (action: () => void) => {
    action()
    setIsOpen(false)
  }

  return (
    <div className="relative" ref={dropdownRef}>
      {/* Triangolino piccolo e discreto */}
      <button
        ref={buttonRef}
        onClick={handleToggle}
        className="p-1 rounded hover:bg-slate-100 transition-colors cursor-pointer"
        title="Azioni"
      >
        <ChevronDown className={`h-3 w-3 text-slate-600 transition-transform duration-200 ${
          isOpen ? 'rotate-180' : ''
        }`} />
      </button>

      {/* Dropdown Menu - renderizzato con portal */}
      {isOpen && typeof window !== 'undefined' && createPortal(
        <div
          className="fixed w-36 bg-white border border-slate-200 rounded-md shadow-lg z-[100]"
          style={{
            top: dropdownPosition.top,
            left: dropdownPosition.left,
          }}
        >
          <div className="py-1">
            {/* Modifica - sempre disponibile */}
            <button
              onClick={() => handleAction(onEdit)}
              className="w-full px-3 py-2 text-left text-sm flex items-center gap-2 hover:bg-slate-50 transition-colors"
            >
              <Edit className="h-3.5 w-3.5 text-slate-600" />
              <span>Modifica</span>
            </button>

            {/* Abilita/Disabilita */}
            <button
              onClick={() => handleAction(onToggleStatus)}
              disabled={user.ruolo === 'owner'}
              className={`w-full px-3 py-2 text-left text-sm flex items-center gap-2 transition-colors ${
                user.ruolo === 'owner'
                  ? 'opacity-50 cursor-not-allowed bg-slate-50'
                  : 'hover:bg-slate-50'
              }`}
            >
              {user.abilitato ? (
                <>
                  <Clock className="h-3.5 w-3.5 text-red-500" />
                  <span>Disabilita</span>
                </>
              ) : (
                <>
                  <CheckCircle className="h-3.5 w-3.5 text-green-500" />
                  <span>Abilita</span>
                </>
              )}
            </button>

            {/* Elimina */}
            <button
              onClick={() => handleAction(onDelete)}
              disabled={user.ruolo === 'owner'}
              className={`w-full px-3 py-2 text-left text-sm flex items-center gap-2 transition-colors ${
                user.ruolo === 'owner'
                  ? 'opacity-50 cursor-not-allowed bg-slate-50'
                  : 'hover:bg-red-50 text-red-600'
              }`}
            >
              <Trash2 className="h-3.5 w-3.5 text-red-500" />
              <span>Elimina</span>
            </button>
          </div>
        </div>,
        document.body
      )}
    </div>
  )
}
